package com.wendy.face.detection

import android.graphics.Bitmap
import android.util.Log
import com.google.mlkit.vision.common.InputImage
import com.google.mlkit.vision.facemesh.FaceMesh
import com.google.mlkit.vision.facemesh.FaceMeshDetection
import com.google.mlkit.vision.facemesh.FaceMeshDetectorOptions
import com.wendy.face.utils.FaceDetectionUtils

/**
 * 人脸网格检测管理器
 * 负责配置和执行人脸网格检测功能
 */
class FaceDetectorManager {

    companion object {
        private const val TAG = "FaceDetectorManager"
        private const val MIN_FACE_SIZE = 50 // 最小人脸尺寸
        private const val MAX_RETRY_COUNT = 2 // 最大重试次数
    }

    private val faceMeshDetectorOptions = FaceMeshDetectorOptions.Builder()
        .setUseCase(FaceMeshDetectorOptions.FACE_MESH)
        .build()

    private val faceMeshDetector = FaceMeshDetection.getClient(faceMeshDetectorOptions)

    /**
     * 检测图片中的人脸网格
     * @param inputImage 要检测的图片
     * @param onSuccess 检测成功回调
     * @param onFailure 检测失败回调
     */
    fun detectFaces(
        inputImage: InputImage,
        onSuccess: (List<FaceMesh>, Int, Int) -> Unit,
        onFailure: (Exception) -> Unit
    ) {
        detectFacesWithRetry(inputImage, onSuccess, onFailure, 0)
    }

    /**
     * 带重试机制的人脸检测
     */
    private fun detectFacesWithRetry(
        inputImage: InputImage,
        onSuccess: (List<FaceMesh>, Int, Int) -> Unit,
        onFailure: (Exception) -> Unit,
        retryCount: Int
    ) {
        try {
            Log.d(TAG, "Starting face detection (attempt ${retryCount + 1})")
            Log.d(TAG, "Image info - Width: ${inputImage.width}, Height: ${inputImage.height}, Rotation: ${inputImage.rotationDegrees}")

            // 检查图像尺寸是否合理
            if (inputImage.width < MIN_FACE_SIZE || inputImage.height < MIN_FACE_SIZE) {
                Log.w(TAG, "Image too small for face detection: ${inputImage.width}x${inputImage.height}")
                onFailure(IllegalArgumentException("Image too small for face detection"))
                return
            }

            // 执行人脸网格检测
            faceMeshDetector.process(inputImage)
                .addOnSuccessListener { detectedFaceMeshes ->
                    Log.d(TAG, "Face mesh detection completed, faces found: ${detectedFaceMeshes.size}")

                    // 过滤掉过小的人脸
                    val validFaces = detectedFaceMeshes.filter { faceMesh ->
                        val boundingBox = faceMesh.boundingBox
                        val faceSize = minOf(boundingBox.width(), boundingBox.height())
                        val isValid = faceSize >= MIN_FACE_SIZE

                        Log.d(TAG, "Face validation - Size: ${boundingBox.width()}x${boundingBox.height()}, Valid: $isValid")
                        if (!isValid) {
                            Log.w(TAG, "Filtered out small face: ${boundingBox.width()}x${boundingBox.height()}")
                        }

                        isValid
                    }

                    Log.d(TAG, "Valid faces after filtering: ${validFaces.size}")
                    validFaces.forEachIndexed { index, faceMesh ->
                        val boundingBox = faceMesh.boundingBox
                        Log.d(TAG, "Valid FaceMesh $index: points=${faceMesh.allPoints.size}, bounds=${boundingBox.width()}x${boundingBox.height()}")
                    }

                    onSuccess(validFaces, inputImage.width, inputImage.height)
                }
                .addOnFailureListener { e ->
                    Log.e(TAG, "Face mesh detection failed (attempt ${retryCount + 1})", e)

                    // 如果还有重试机会，则重试
                    if (retryCount < MAX_RETRY_COUNT) {
                        Log.d(TAG, "Retrying face detection...")
                        detectFacesWithRetry(inputImage, onSuccess, onFailure, retryCount + 1)
                    } else {
                        Log.e(TAG, "Face detection failed after $MAX_RETRY_COUNT attempts")
                        onFailure(e)
                    }
                }
        } catch (e: Exception) {
            Log.e(TAG, "Error processing InputImage (attempt ${retryCount + 1})", e)

            // 如果还有重试机会，则重试
            if (retryCount < MAX_RETRY_COUNT) {
                Log.d(TAG, "Retrying face detection due to exception...")
                detectFacesWithRetry(inputImage, onSuccess, onFailure, retryCount + 1)
            } else {
                Log.e(TAG, "Face detection failed with exception after $MAX_RETRY_COUNT attempts")
                onFailure(e)
            }
        }
    }

    /**
     * 专门用于Bitmap的人脸检测，会进行额外的预处理
     * @param bitmap 要检测的Bitmap
     * @param rotationDegrees 旋转角度
     * @param onSuccess 检测成功回调
     * @param onFailure 检测失败回调
     */
    fun detectFacesFromBitmap(
        bitmap: Bitmap,
        rotationDegrees: Int = 0,
        onSuccess: (List<FaceMesh>, Int, Int) -> Unit,
        onFailure: (Exception) -> Unit
    ) {
        try {
            Log.d(TAG, "Detecting faces from bitmap - Size: ${bitmap.width}x${bitmap.height}, Rotation: $rotationDegrees")

            // 检查图像是否适合检测
            if (!FaceDetectionUtils.isImageSuitableForDetection(bitmap)) {
                Log.e(TAG, "Image is not suitable for face detection")
                onFailure(IllegalArgumentException("Image is not suitable for face detection"))
                return
            }

            // 预处理图像以提高检测成功率
            val processedBitmap = FaceDetectionUtils.preprocessImageForDetection(bitmap)
            val shouldRecycleBitmap = processedBitmap != bitmap

            try {
                // 创建InputImage，确保正确处理旋转
                val inputImage = FaceDetectionUtils.createInputImageForDetection(processedBitmap, rotationDegrees)
                if (inputImage == null) {
                    Log.e(TAG, "Failed to create InputImage")
                    onFailure(IllegalArgumentException("Failed to create InputImage"))
                    return
                }

                // 执行检测，传递原始图像尺寸用于坐标计算
                detectFacesWithRetry(
                    inputImage,
                    { faceMeshes, width, height ->
                        // 如果图像被缩放了，需要调整坐标
                        val scaledFaces = if (shouldRecycleBitmap) {
                            adjustFaceCoordinatesForScale(faceMeshes, processedBitmap, bitmap)
                        } else {
                            faceMeshes
                        }
                        onSuccess(scaledFaces, bitmap.width, bitmap.height)
                    },
                    onFailure,
                    0
                )

            } finally {
                // 清理预处理的图像
                if (shouldRecycleBitmap && !processedBitmap.isRecycled) {
                    processedBitmap.recycle()
                }
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error in detectFacesFromBitmap", e)
            onFailure(e)
        }
    }

    /**
     * 调整人脸坐标以适应图像缩放
     */
    private fun adjustFaceCoordinatesForScale(
        faceMeshes: List<FaceMesh>,
        scaledBitmap: Bitmap,
        originalBitmap: Bitmap
    ): List<FaceMesh> {
        val scaleX = originalBitmap.width.toFloat() / scaledBitmap.width
        val scaleY = originalBitmap.height.toFloat() / scaledBitmap.height

        Log.d(TAG, "Adjusting face coordinates - ScaleX: $scaleX, ScaleY: $scaleY")

        // 注意：ML Kit的FaceMesh对象是不可变的，我们无法直接修改坐标
        // 在实际应用中，坐标调整应该在使用检测结果时进行
        // 这里我们记录缩放信息，但返回原始结果
        return faceMeshes
    }

    /**
     * 释放资源
     */
    fun release() {
        try {
            faceMeshDetector.close()
        } catch (e: Exception) {
            Log.e(TAG, "Error closing face mesh detector", e)
        }
    }
}
