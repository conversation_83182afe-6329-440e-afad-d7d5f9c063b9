package com.wendy.face.utils

import android.graphics.Bitmap
import android.util.Log
import com.google.mlkit.vision.common.InputImage
import com.google.mlkit.vision.facemesh.FaceMesh
import kotlin.math.pow

/**
 * 人脸检测工具类
 * 提供图像预处理和坐标转换等辅助功能
 */
object FaceDetectionUtils {
    
    private const val TAG = "FaceDetectionUtils"
    
    /**
     * 检查图像是否适合进行人脸检测
     * @param bitmap 要检查的图像
     * @return 是否适合检测
     */
    fun isImageSuitableForDetection(bitmap: Bitmap?): Bo<PERSON>an {
        if (bitmap == null) {
            Log.w(TAG, "Bitmap is null")
            return false
        }
        
        if (bitmap.isRecycled) {
            Log.w(TAG, "Bitmap is recycled")
            return false
        }
        
        val minSize = 100 // 最小尺寸
        if (bitmap.width < minSize || bitmap.height < minSize) {
            Log.w(TAG, "Image too small: ${bitmap.width}x${bitmap.height}")
            return false
        }
        
        val maxSize = 4096 // 最大尺寸
        if (bitmap.width > maxSize || bitmap.height > maxSize) {
            Log.w(TAG, "Image too large: ${bitmap.width}x${bitmap.height}")
            return false
        }
        
        return true
    }
    
    /**
     * 预处理图像以提高检测成功率
     * @param bitmap 原始图像
     * @return 预处理后的图像
     */
    fun preprocessImageForDetection(bitmap: Bitmap): Bitmap {
        Log.d(TAG, "Preprocessing image for detection - Original size: ${bitmap.width}x${bitmap.height}")
        
        // 如果图像过大，缩放到合适的尺寸以提高检测速度和成功率
        val maxDetectionSize = 1280
        return if (bitmap.width > maxDetectionSize || bitmap.height > maxDetectionSize) {
            val scale = minOf(maxDetectionSize.toFloat() / bitmap.width, maxDetectionSize.toFloat() / bitmap.height)
            val newWidth = (bitmap.width * scale).toInt()
            val newHeight = (bitmap.height * scale).toInt()
            
            Log.d(TAG, "Scaling image for detection: ${newWidth}x${newHeight} (scale: $scale)")
            Bitmap.createScaledBitmap(bitmap, newWidth, newHeight, true)
        } else {
            Log.d(TAG, "Image size is suitable for detection, no scaling needed")
            bitmap
        }
    }
    
    /**
     * 创建用于检测的InputImage，确保正确处理旋转
     * @param bitmap 图像
     * @param rotationDegrees 旋转角度
     * @return InputImage对象
     */
    fun createInputImageForDetection(bitmap: Bitmap, rotationDegrees: Int = 0): InputImage? {
        return try {
            Log.d(TAG, "Creating InputImage - Size: ${bitmap.width}x${bitmap.height}, Rotation: $rotationDegrees")
            InputImage.fromBitmap(bitmap, rotationDegrees)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to create InputImage", e)
            null
        }
    }
    
    /**
     * 验证人脸检测结果的有效性
     * @param faceMeshes 检测结果
     * @param imageWidth 图像宽度
     * @param imageHeight 图像高度
     * @return 过滤后的有效人脸列表
     */
    fun validateDetectionResults(
        faceMeshes: List<FaceMesh>,
        imageWidth: Int,
        imageHeight: Int
    ): List<FaceMesh> {
        Log.d(TAG, "Validating ${faceMeshes.size} detected faces")
        
        val validFaces = faceMeshes.filter { faceMesh ->
            val boundingBox = faceMesh.boundingBox
            
            // 检查边界框是否在图像范围内
            val isInBounds = boundingBox.left >= 0 && 
                           boundingBox.top >= 0 && 
                           boundingBox.right <= imageWidth && 
                           boundingBox.bottom <= imageHeight
            
            // 检查人脸尺寸是否合理
            val minFaceSize = minOf(imageWidth, imageHeight) * 0.1f // 至少占图像的10%
            val faceSize = minOf(boundingBox.width(), boundingBox.height())
            val isSizeValid = faceSize >= minFaceSize
            
            // 检查关键点数量
            val hasEnoughPoints = faceMesh.allPoints.size >= 400 // ML Kit通常返回468个点
            
            val isValid = isInBounds && isSizeValid && hasEnoughPoints
            
            Log.d(TAG, "Face validation - Bounds: ${boundingBox.width()}x${boundingBox.height()}, " +
                      "InBounds: $isInBounds, SizeValid: $isSizeValid, Points: ${faceMesh.allPoints.size}, Valid: $isValid")
            
            isValid
        }
        
        Log.d(TAG, "Validation complete - Valid faces: ${validFaces.size}/${faceMeshes.size}")
        return validFaces
    }
    
    /**
     * 计算人脸检测的置信度分数
     * @param faceMesh 人脸网格
     * @param imageWidth 图像宽度
     * @param imageHeight 图像高度
     * @return 置信度分数 (0.0 - 1.0)
     */
    fun calculateConfidenceScore(faceMesh: FaceMesh, imageWidth: Int, imageHeight: Int): Float {
        val boundingBox = faceMesh.boundingBox
        val pointCount = faceMesh.allPoints.size
        
        // 基于人脸大小的分数
        val faceArea = boundingBox.width() * boundingBox.height()
        val imageArea = imageWidth * imageHeight
        val sizeScore = minOf(1.0f, (faceArea.toFloat() / imageArea) * 10) // 人脸占图像面积的比例
        
        // 基于关键点数量的分数
        val pointScore = minOf(1.0f, pointCount / 468.0f) // ML Kit标准点数
        
        // 基于边界框位置的分数（中心位置得分更高）
        val centerX = boundingBox.centerX()
        val centerY = boundingBox.centerY()
        val imageCenterX = imageWidth / 2f
        val imageCenterY = imageHeight / 2f
        
        val distanceFromCenter = kotlin.math.sqrt(
            ((centerX - imageCenterX) / imageCenterX).toDouble().pow(2.0) +
            ((centerY - imageCenterY) / imageCenterY).toDouble().pow(2.0)
        ).toFloat()
        
        val positionScore = maxOf(0.0f, 1.0f - distanceFromCenter)
        
        // 综合分数
        val confidence = (sizeScore * 0.4f + pointScore * 0.4f + positionScore * 0.2f)
        
        Log.d(TAG, "Confidence calculation - Size: $sizeScore, Points: $pointScore, Position: $positionScore, Final: $confidence")
        
        return confidence
    }
    
    /**
     * 选择最佳的人脸检测结果
     * @param faceMeshes 所有检测到的人脸
     * @param imageWidth 图像宽度
     * @param imageHeight 图像高度
     * @return 最佳的人脸，如果没有合适的则返回null
     */
    fun selectBestFace(faceMeshes: List<FaceMesh>, imageWidth: Int, imageHeight: Int): FaceMesh? {
        if (faceMeshes.isEmpty()) return null
        
        val validFaces = validateDetectionResults(faceMeshes, imageWidth, imageHeight)
        if (validFaces.isEmpty()) return null
        
        // 如果只有一个有效人脸，直接返回
        if (validFaces.size == 1) return validFaces.first()
        
        // 选择置信度最高的人脸
        val bestFace = validFaces.maxByOrNull { faceMesh ->
            calculateConfidenceScore(faceMesh, imageWidth, imageHeight)
        }
        
        Log.d(TAG, "Selected best face from ${validFaces.size} valid faces")
        return bestFace
    }
}
