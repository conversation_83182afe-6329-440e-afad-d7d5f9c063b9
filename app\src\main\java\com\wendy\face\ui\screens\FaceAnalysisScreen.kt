package com.wendy.face.ui.screens

import android.graphics.Bitmap
import android.net.Uri
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.AutoAwesome
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.animation.core.*
import com.google.mlkit.vision.facemesh.FaceMesh
import com.wendy.face.analyzer.FaceAnalyzer
import com.wendy.face.analyzer.PalaceAnalysisResult
import com.wendy.face.llm.LLMService
import com.wendy.face.model.ThreeCourtFiveEyeResult
import com.wendy.face.model.TwelvePalacesData
import com.wendy.face.ui.components.FaceOverlay
import com.wendy.face.ui.components.LayerControlPanel
import kotlinx.coroutines.launch

/**
 * 人脸分析结果展示界面
 * @param capturedBitmap 拍摄的照片
 * @param faceMeshes 检测到的人脸网格
 * @param analysisResults 分析结果
 * @param isBackCamera 是否使用后置摄像头
 * @param onBack 返回按钮的回调
 * @param onReanalyze 重新分析按钮的回调
 */
@Composable
fun FaceAnalysisScreen(
    capturedImageUri: Uri,
    capturedBitmap: Bitmap, // 仍需要bitmap用于获取尺寸信息
    faceMeshes: List<FaceMesh>,
    analysisResults: List<PalaceAnalysisResult>,
    isBackCamera: Boolean,
    onBack: () -> Unit,
    onReanalyze: (() -> Unit)? = null
) {
    var imageDisplaySize by remember { mutableStateOf(IntSize.Zero) }
    var imageDisplayOffset by remember { mutableStateOf(androidx.compose.ui.geometry.Offset.Zero) }
    val coroutineScope = rememberCoroutineScope()
    val context = LocalContext.current
    val llmService = remember { LLMService() }
    var destinyText by remember { mutableStateOf("") }
    var isAnalyzing by remember { mutableStateOf(false) }
    var showDestinyResult by remember { mutableStateOf(false) }
    val listState = rememberLazyListState()

    // 图层控制状态
    var showPhoto by remember { mutableStateOf(true) }
    var showAnalysisReport by remember { mutableStateOf(true) }
    var showContours by remember { mutableStateOf(true) }
    var showAllKeypoints by remember { mutableStateOf(false) } // 默认不显示所有关键点
   var showThreeCourtFiveEye by remember { mutableStateOf(true) }
   var threeCourtFiveEyeResult by remember { mutableStateOf<ThreeCourtFiveEyeResult?>(null) }
   val faceAnalyzer = remember { FaceAnalyzer() }

   LaunchedEffect(faceMeshes) {
       if (faceMeshes.isNotEmpty()) {
           threeCourtFiveEyeResult = faceAnalyzer.analyzeThreeCourtFiveEye(faceMeshes.first())
       }
   }

    Box(modifier = Modifier.fillMaxSize()) {
        // 1. 将拍摄的照片作为背景（根据图层控制显示/隐藏）
        if (showPhoto) {
            Image(
                bitmap = capturedBitmap.asImageBitmap(),
                contentDescription = "Captured Photo",
                modifier = Modifier
                    .fillMaxSize()
                    .onGloballyPositioned { layoutCoordinates ->
                        // 获取Image Composable的实际尺寸和位置
                        imageDisplaySize = layoutCoordinates.size
                        imageDisplayOffset = layoutCoordinates.localToRoot(androidx.compose.ui.geometry.Offset.Zero)
                    },
                contentScale = ContentScale.Crop
            )
        } else {
            // 当照片隐藏时，仍需要获取尺寸信息用于关键点定位
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.Black)
                    .onGloballyPositioned { layoutCoordinates ->
                        imageDisplaySize = layoutCoordinates.size
                        imageDisplayOffset = layoutCoordinates.localToRoot(androidx.compose.ui.geometry.Offset.Zero)
                    }
            )
        }

        // 2. 在照片上叠加人脸关键点
        if (imageDisplaySize != IntSize.Zero && faceMeshes.isNotEmpty()) {
            val scaleAndOffset = calculateScaleAndOffset(
                imageWidth = capturedBitmap.width,
                imageHeight = capturedBitmap.height,
                viewWidth = imageDisplaySize.width.toFloat(),
                viewHeight = imageDisplaySize.height.toFloat()
            )

            FaceOverlay(
                faceMeshes = faceMeshes,
                scaleX = scaleAndOffset.scaleX,
                scaleY = scaleAndOffset.scaleY,
                offsetX = scaleAndOffset.offsetX,
                offsetY = scaleAndOffset.offsetY,
                isBackCamera = isBackCamera,
                isPreviewMode = false,
                show3DPoints = false,
                showAllKeypoints = showAllKeypoints,
                showContours = showContours,
                showPalaceMarkers = true, // 宫位标记始终显示
               showThreeCourtFiveEye = showThreeCourtFiveEye,
               threeCourtFiveEyeResult = threeCourtFiveEyeResult
            )
        }

        // 添加一个从下到上的渐变蒙层，让文字更清晰
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(
                    Brush.verticalGradient(
                        colors = listOf(Color.Transparent, Color.Black.copy(alpha = 0.8f)),
                        startY = 600f // 从屏幕大约1/3处开始渐变
                    )
                )
        )

        // 顶部返回按钮
        Box(
            modifier = Modifier
                .align(Alignment.TopStart)
                .padding(16.dp)
        ) {
            IconButton(onClick = onBack) {
                Icon(
                    imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                    contentDescription = "返回相机",
                    tint = Color.White
                )
            }
        }

        // 右侧图层控制面板（从上边30%位置开始）
        LayerControlPanel(
            showPhoto = showPhoto,
            showAnalysisReport = showAnalysisReport,
            showContours = showContours,
            showAllKeypoints = showAllKeypoints,
            onPhotoToggle = { showPhoto = it },
            onAnalysisReportToggle = { showAnalysisReport = it },
            onContoursToggle = { showContours = it },
            onAllKeypointsToggle = { showAllKeypoints = it },
           showThreeCourtFiveEye = showThreeCourtFiveEye,
           onThreeCourtFiveEyeToggle = { showThreeCourtFiveEye = it },
            modifier = Modifier
                .align(Alignment.TopEnd)
                .padding(top = LocalConfiguration.current.screenHeightDp.dp * 0.2f, end = 8.dp)
        )

        // 底部内容区域
        Column(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .fillMaxWidth()
        ) {

            Spacer(modifier = Modifier.weight(1f))

            // 显示检测状态信息
            if (faceMeshes.isEmpty()) {
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    colors = CardDefaults.cardColors(containerColor = Color.Red.copy(alpha = 0.8f))
                ) {
                    Text(
                        text = "未检测到人脸\n请确保照片中有清晰的人脸，然后点击\"重新分析\"",
                        color = Color.White,
                        modifier = Modifier.padding(16.dp),
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            } else if (analysisResults.isEmpty()) {
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    colors = CardDefaults.cardColors(containerColor = Color(0xFFFF9800).copy(alpha = 0.8f))
                ) {
                    Text(
                        text = "检测到 ${faceMeshes.size} 张人脸，但分析结果为空\n点击\"重新分析\"重试",
                        color = Color.White,
                        modifier = Modifier.padding(16.dp),
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }

            // 3. 底部展示分析结果的滚动列表（根据图层控制显示/隐藏）
            if (analysisResults.isNotEmpty() && showAnalysisReport) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .heightIn(max = 400.dp) // 限制最大高度
                        .padding(horizontal = 8.dp)
                        .background(
                            Color.Black.copy(alpha = 0.6f),
                            RoundedCornerShape(16.dp)
                        )
                        .padding(12.dp)
                ) {
                    LazyColumn(
                        state = listState,
                        modifier = Modifier.fillMaxWidth(),
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        // 只在未开始分析命格时显示十二宫分析结果
                        if (!showDestinyResult) {
                           // 显示三庭五眼分析结果
                           threeCourtFiveEyeResult?.let {
                               item {
                                   ThreeCourtFiveEyeResultCard(result = it)
                               }
                           }
                            items(analysisResults) { result ->
                                // The new result structure is handled directly in the Card
                                AnalysisResultCard(palaceName = result.palaceName, description = result.description)
                            }
                        }

                        item {
                            ModernDestinyButton(
                                isAnalyzing = isAnalyzing,
                                onClick = {
                                    if (!isAnalyzing) {
                                        isAnalyzing = true
                                        showDestinyResult = true
                                        destinyText = ""

                                        coroutineScope.launch {
                                            try {
                                                val palaces = TwelvePalacesData(
                                                    analysisResults.associate { it.palaceName to it.description }
                                                )
                                                val sharedPreferences = context.getSharedPreferences("face_app_settings", android.content.Context.MODE_PRIVATE)
                                                val personalization = sharedPreferences.getString("personalization", "") ?: ""

                                                llmService.getDestiny(palaces, threeCourtFiveEyeResult, personalization).collect { chunk ->
                                                    destinyText += chunk
                                                    // 自动滚动到底部
                                                    if (chunk.isNotEmpty()) {
                                                        try {
                                                            listState.animateScrollToItem(
                                                                index = listState.layoutInfo.totalItemsCount - 1,
                                                                scrollOffset = Int.MAX_VALUE // 滚动到item的底部
                                                            )
                                                        } catch (e: Exception) {
                                                            // 忽略滚动异常
                                                        }
                                                    }
                                                }
                                            } catch (e: Exception) {
                                                destinyText = "分析失败，请稍后重试。"
                                            } finally {
                                                isAnalyzing = false
                                            }
                                        }
                                    }
                                }
                            )
                        }

                        // 显示命格分析结果
                        if (showDestinyResult) {
                            item {
                                DestinyResultCard(
                                    destinyText = destinyText,
                                    isAnalyzing = isAnalyzing
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}

private data class ScaleAndOffsetResult(
    val scaleX: Float,
    val scaleY: Float,
    val offsetX: Float,
    val offsetY: Float
)

/**
 * 计算在ContentScale.Crop模式下的缩放比例和偏移量
 */
private fun calculateScaleAndOffset(
    imageWidth: Int,
    imageHeight: Int,
    viewWidth: Float,
    viewHeight: Float
): ScaleAndOffsetResult {
    val imageAspectRatio = imageWidth.toFloat() / imageHeight.toFloat()
    val viewAspectRatio = viewWidth / viewHeight

    val scale: Float
    val offsetX: Float
    val offsetY: Float

    if (imageAspectRatio > viewAspectRatio) {
        // 图片比视图更宽，高度填满视图，宽度裁剪
        scale = viewHeight / imageHeight.toFloat()
        val scaledImageWidth = imageWidth * scale
        offsetX = (viewWidth - scaledImageWidth) / 2f
        offsetY = 0f
    } else {
        // 图片比视图更高，宽度填满视图，高度裁剪
        scale = viewWidth / imageWidth.toFloat()
        val scaledImageHeight = imageHeight * scale
        offsetX = 0f
        offsetY = (viewHeight - scaledImageHeight) / 2f
    }

    return ScaleAndOffsetResult(scale, scale, offsetX, offsetY)
}


/**
 * 单个宫位分析结果的卡片UI
 */
@Composable
private fun AnalysisResultCard(palaceName: String, description: String) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp)
    ) {
        Text(
            text = palaceName,
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold,
            color = Color.White
        )
        Spacer(modifier = Modifier.height(4.dp))
        Text(
            text = description,
            style = MaterialTheme.typography.bodyMedium,
            color = Color.White.copy(alpha = 0.9f),
            lineHeight = 20.sp
        )
    }
}

/**
 * 现代化的推断命格按钮
 */
@Composable
private fun ModernDestinyButton(
    isAnalyzing: Boolean,
    onClick: () -> Unit
) {
    // 按钮动画效果
    val infiniteTransition = rememberInfiniteTransition(label = "button_animation")
    val scale by infiniteTransition.animateFloat(
        initialValue = 1f,
        targetValue = if (isAnalyzing) 1.05f else 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(1000, easing = EaseInOut),
            repeatMode = RepeatMode.Reverse
        ),
        label = "scale_animation"
    )

    val shimmerColors = listOf(
        Color(0xFF6366F1).copy(alpha = 0.9f),
        Color(0xFF8B5CF6).copy(alpha = 0.9f),
        Color(0xFFA855F7).copy(alpha = 0.9f),
        Color(0xFFEC4899).copy(alpha = 0.9f)
    )

    Button(
        onClick = onClick,
        enabled = !isAnalyzing,
        modifier = Modifier
            .fillMaxWidth()
            .height(56.dp)
            .scale(scale)
            .clip(RoundedCornerShape(28.dp)),
        colors = ButtonDefaults.buttonColors(
            containerColor = Color.Transparent,
            disabledContainerColor = Color.Transparent
        ),
        contentPadding = PaddingValues(0.dp)
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(
                    brush = Brush.horizontalGradient(shimmerColors),
                    shape = RoundedCornerShape(28.dp)
                ),
            contentAlignment = Alignment.Center
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Center
            ) {
                if (isAnalyzing) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(20.dp),
                        color = Color.White,
                        strokeWidth = 2.dp
                    )
                    Spacer(modifier = Modifier.width(12.dp))
                    Text(
                        text = "AI分析中...",
                        color = Color.White,
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.SemiBold
                    )
                } else {
                    Icon(
                        imageVector = Icons.Default.AutoAwesome,
                        contentDescription = null,
                        tint = Color.White,
                        modifier = Modifier.size(24.dp)
                    )
                    Spacer(modifier = Modifier.width(12.dp))
                    Text(
                        text = "✨ AI推断命格",
                        color = Color.White,
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.SemiBold
                    )
                }
            }
        }
    }
}

/**
 * 命格分析结果卡片
 */
@Composable
private fun DestinyResultCard(
    destinyText: String,
    isAnalyzing: Boolean
) {
    val scrollState = rememberScrollState()

    // 当文字更新时自动滚动到底部
    LaunchedEffect(destinyText) {
        if (destinyText.isNotEmpty()) {
            scrollState.animateScrollTo(scrollState.maxValue)
        }
    }

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp)
            .pointerInput(Unit) {
                // 拦截点击事件，防止传播到父组件
                detectTapGestures { }
            },
        colors = CardDefaults.cardColors(
            containerColor = Color.Black.copy(alpha = 0.7f)
        ),
        shape = RoundedCornerShape(16.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.padding(bottom = 16.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.AutoAwesome,
                    contentDescription = null,
                    tint = Color(0xFF8B5CF6),
                    modifier = Modifier.size(24.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "AI命格分析",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = Color.White
                )
            }

            if (destinyText.isNotEmpty()) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .heightIn(max = 300.dp) // 限制最大高度
                        .verticalScroll(scrollState)
                ) {
                    Text(
                        text = destinyText,
                        style = MaterialTheme.typography.bodyLarge,
                        color = Color.White.copy(alpha = 0.95f),
                        lineHeight = 24.sp,
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            }

            if (isAnalyzing && destinyText.isEmpty()) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(16.dp),
                        color = Color(0xFF8B5CF6),
                        strokeWidth = 2.dp
                    )
                    Spacer(modifier = Modifier.width(12.dp))
                    Text(
                        text = "正在连接AI大师，请稍候...",
                        style = MaterialTheme.typography.bodyMedium,
                        color = Color.White.copy(alpha = 0.7f)
                    )
                }
            }
        }
    }
}

@Composable
fun ThreeCourtFiveEyeResultCard(result: ThreeCourtFiveEyeResult) {
   Column(
       modifier = Modifier
           .fillMaxWidth()
           .padding(vertical = 4.dp)
   ) {
       Text(
           text = "三庭五眼分析",
           style = MaterialTheme.typography.titleMedium,
           fontWeight = FontWeight.Bold,
           color = Color.White
       )
       Spacer(modifier = Modifier.height(4.dp))
       Text(
           text = result.threeCourt.description,
           style = MaterialTheme.typography.bodyMedium,
           color = Color.White.copy(alpha = 0.9f),
           lineHeight = 20.sp
       )
       Spacer(modifier = Modifier.height(4.dp))
       Text(
           text = result.fiveEye.description,
           style = MaterialTheme.typography.bodyMedium,
           color = Color.White.copy(alpha = 0.9f),
           lineHeight = 20.sp
       )
   }
}